package io.dataease.datasource.dao.ext.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.dataease.datasource.dao.ext.po.Ctimestamp;
import io.dataease.datasource.dao.ext.po.DataSourceNodePO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface DataSourceExtMapper extends BaseMapper<DataSourceNodePO> {


    @Select("select  unix_timestamp(current_timestamp())  as currentTimestamp")
    @Results(
            id = "selectTimestamp",
            value = {
                    @Result(property = "currentTimestamp", column = "currentTimestamp")
            }
    )
    Ctimestamp selectTimestamp();

    @Select("""
            select id, name, pid, org_id, type, create_by from core_datasource
            ${ew.customSqlSegment}
            """)
    List<DataSourceNodePO> queryNodes(@Param("ew") QueryWrapper<DataSourceNodePO> queryWrapper);
}
