package io.dataease.datasource.provider;
import io.dataease.extensions.datasource.dto.DatasourceSchemaDTO;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
@Component("kingbaseProvider")
public class KingbaseProvider extends CalciteProvider {

    /**
     * 重写transSqlDialect方法，添加人大金仓特殊处理
     */
    @Override
    public String transSqlDialect(String sql, Map<Long, DatasourceSchemaDTO> dsMap) {
        // 先调用父类方法获取基本转换结果
        String result = super.transSqlDialect(sql, dsMap);

        // 人大金仓特殊处理
        DatasourceSchemaDTO value = dsMap.entrySet().iterator().next().getValue();
        if ("kingBase".equals(value.getType())) {
            // 移除双引号
            result = result.replace("\"", "");
            // 处理Unicode转义序列
            result = processUnicodeEscapes(result);
        }

        return result;
    }

    /**
     * 处理SQL中的Unicode转义序列
     */
    private String processUnicodeEscapes(String sql) {
        // 匹配u&'\xxxx\xxxx...'格式
        Pattern pattern = Pattern.compile("u&'(\\\\[0-9a-fA-F]{4})+?'");
        Matcher matcher = pattern.matcher(sql);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            try {
                String unicodeEscape = matcher.group();
                // 提取Unicode码点
                String content = unicodeEscape.substring(3, unicodeEscape.length() - 1);

                StringBuilder plainText = new StringBuilder();
                // 分割成单独的码点(\xxxx)
                String[] hexValues = content.split("\\\\");
                // 第一个元素是空字符串，因为字符串以\开头
                for (int i = 1; i < hexValues.length; i++) {
                    // 将16进制转换为int，再转为字符
                    int codePoint = Integer.parseInt(hexValues[i], 16);
                    plainText.appendCodePoint(codePoint);
                }

                // 使用单引号括起来，确保SQL语法正确
                String replacement = "'" + plainText.toString().replace("'", "''") + "'";
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            } catch (Exception e) {
                // 处理失败时不替换，保持原样
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
