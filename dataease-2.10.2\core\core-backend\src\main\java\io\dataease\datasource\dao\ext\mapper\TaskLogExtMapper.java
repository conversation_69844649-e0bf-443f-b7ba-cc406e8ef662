package io.dataease.datasource.dao.ext.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.dataease.api.ds.vo.CoreDatasourceTaskLogDTO;
import org.apache.ibatis.annotations.*;


@Mapper
public interface TaskLogExtMapper extends BaseMapper<CoreDatasourceTaskLogDTO> {


    @Select(
            """     
                    select * 
                    from core_datasource_task_log
                    ${ew.customSqlSegment} 
                    """
    )
    @Results(
            id = "taskLog",
            value = {
                    @Result(property = "id", column = "id"),
                    @Result(property = "ds_id", column = "dsId"),
                    @Result(property = "task_id", column = "taskId"),
                    @Result(property = "start_time", column = "startTime"),
                    @Result(property = "end_time", column = "endTime"),
                    @Result(property = "task_status", column = "taskStatus"),
                    @Result(property = "trigger_type", column = "triggerType"),
                    @Result(property = "table_name", column = "tableName"),
                    @Result(property = "info", column = "info")
            }
    )
    IPage<CoreDatasourceTaskLogDTO> pager(IPage<CoreDatasourceTaskLogDTO> page, @Param("ew") QueryWrapper queryWrapper);




}
