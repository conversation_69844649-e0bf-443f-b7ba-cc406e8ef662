package io.dataease.datasource.type;

import io.dataease.extensions.datasource.vo.DatasourceConfiguration;
import io.dataease.utils.StringUtils;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Data
@Component("kingBase")
public class Kingbase extends DatasourceConfiguration {
    private String driver = "com.kingbase8.Driver";
    private String extraParams = "";
    private List<String> illegalParameters = Arrays.asList("allowLoadLocalInfile");
    private List<String> showTableSqls = Arrays.asList("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
    private String schema = "public";

public String getJdbc() {
    if(StringUtils.isNoneEmpty(getUrlType()) && !getUrlType().equalsIgnoreCase("hostName")){
        return getJdbcUrl();
    }
    if(StringUtils.isEmpty(extraParams.trim())){
        if (StringUtils.isEmpty(getSchema())) {
            return "***************************************************************"
                    .replace("HOSTNAME", getLHost().trim())
                    .replace("PORT", getLPort().toString().trim())
                    .replace("DATABASE", getDataBase().trim());
        } else {
            return "************************************************************************************"
                    .replace("HOSTNAME", getLHost().trim())
                    .replace("PORT", getLPort().toString().trim())
                    .replace("DATABASE", getDataBase().trim())
                    .replace("SCHEMA", getSchema().trim());
        }
    } else {
        return "****************************************************************************"
                .replace("HOSTNAME", getLHost().trim())
                .replace("PORT", getLPort().toString().trim())
                .replace("DATABASE", getDataBase().trim())
                .replace("EXTRA_PARAMS", getExtraParams().trim());
    }
}

}
